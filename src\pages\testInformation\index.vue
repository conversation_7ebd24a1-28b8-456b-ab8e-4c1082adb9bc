<route lang="json5">
{
  style: {
    navigationBarTitleText: '测试信息',
  },
}
</route>

<template>
  <view
    class="m-24rpx"
    :style="{
      backgroundImage: `url(${getImagesUrl('68596c87e4b00e5b2cf36192.png')})`,
      backgroundRepeat: 'no-repeat',
      // backgroundSize: 'cover',
      backgroundSize: '100% 100%',
      backgroundClip: 'border-box',
    }"
  >
    <view class="pt-73rpx pb-50rpx">
      <wd-cell-group custom-class="mx-10rpx">
        <wd-cell custom-class="py-20rpx" title="测算对象" center>
          <wd-radio-group v-model="testTarget" inline shape="dot" @change="changeTestTarget">
            <wd-radio value="self">自己</wd-radio>
            <wd-radio value="other">他人</wd-radio>
          </wd-radio-group>
        </wd-cell>
        <wd-cell custom-class="py-20rpx" title="性别" center>
          <wd-radio-group v-model="userArchive.gender" inline shape="dot">
            <wd-radio value="1">男</wd-radio>
            <wd-radio value="2">女</wd-radio>
          </wd-radio-group>
        </wd-cell>
        <wd-datetime-picker
          custom-class="py-20rpx"
          align-right
          type="date"
          title="请选择年月日"
          v-model="userArchive.birthday"
          :minDate="minDate"
          :maxDate="Date.now()"
          label="出生日期"
          :formatter="formatter"
          :default-value="defaultValue"
          confirm-button-text="确定"
          @confirm="onConfirm"
        />
        <wd-datetime-picker
          type="time"
          custom-class="py-20rpx"
          align-right
          title="请选择出生时间-时、分"
          v-model="userArchive.birthTime"
          confirm-button-text="确定"
          :formatter="formatter"
          label="出生时间"
        />
        <view class="txtClass">若时间不详，可将12:00作为参考</view>
        <wd-col-picker
          custom-class="py-20rpx"
          label="出生地点"
          title="请选择出生地点"
          v-model="userArchive.birthAreaArray"
          :columns="areaAddress"
          :column-change="columnChange"
          @confirm="handleConfirm"
          align-right
          auto-complete
        ></wd-col-picker>
        <view class="pb-20rpx pt-20rpx">
          <wd-row :gutter="10">
            <wd-col :span="24">
              <wd-button
                custom-class="custom"
                :round="false"
                @click="goTestInformation"
                :disabled="!idDisabled"
              >
                前往测算
              </wd-button>
            </wd-col>
          </wd-row>
        </view>
      </wd-cell-group>
    </view>
  </view>
  <MessageBoxHb
    ref="MessageBoxHbElement"
    :msg="msg"
    cancelButtonText="知道了"
    :confirmButton="false"
  />
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useColPickerData } from '@/hooks/useColPickerData'
import dayjs from 'dayjs'
import { getImagesUrl } from '@/utils/getImagesUrl'
import { userInfo, IUserInfoItem, IUserInfoQuery, userUpdate } from '@/service/myArchive/index'
import { onShow } from '@dcloudio/uni-app'
import {
  aiChat,
  IAiChatItem,
  IAiChatQuery,
  getTestResult,
} from '@/service/feedbackOnOpinions/index'
import MessageBoxHb from '@/components/MessageBoxHb.vue'

import { useUserStore } from '@/store'
const userStore = useUserStore()

// 定义最小日期
const minDate = dayjs(new Date('1900-01-01')).valueOf()
const defaultValue = ref<number>(Date.now())
const msg = ref('')
// 定义用户档案信息
const userArchive = ref<IUserInfoQuery>({
  gender: '',
  birthday: '',
  birthTime: '',
  birthAreaArray: [],
  birthArea: '',
})
const formatter = (type, value) => {
  switch (type) {
    case 'year':
      return value + '年'
    case 'month':
      return value + '月'
    case 'date':
      return value + '日'
    case 'hour':
      return value + '时'
    case 'minute':
      return value + '分'
    default:
      return value
  }
}
// 新增测算对象选项：self(自己) | other(他人)
const testTarget = ref<string>('self')

const idDisabled = computed(
  () =>
    userArchive.value.gender !== '' &&
    userArchive.value.birthday !== '' &&
    userArchive.value.birthTime !== '' &&
    userArchive.value.birthAreaArray.length > 0,
)

// 获取地区数据方法
const { colPickerData, findChildrenByCode } = useColPickerData()
// 出生地点初始数据
const areaAddress = ref<any[]>([
  colPickerData.map((item) => {
    return {
      value: item.value,
      label: item.text,
    }
  }),
])
// 地址根据地址id初始化数据
function getAddress(item: string[]) {
  const data = [
    colPickerData.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
    findChildrenByCode(colPickerData, item[0])!.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
    findChildrenByCode(colPickerData, item[1])!.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
  ]
  return data
}

function getAddress2(item: string[]) {
  const data = [
    colPickerData.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
  ]
  return data
}

// 地址数据变更事件
const columnChange = ({ selectedItem, resolve, finish }) => {
  const areaData = findChildrenByCode(colPickerData, selectedItem.value)
  if (areaData && areaData.length) {
    resolve(
      areaData.map((item) => {
        return {
          value: item.value,
          label: item.text,
        }
      }),
    )
  } else {
    finish()
  }
}
// 编辑资料(已选择地址初始化)
function onModify() {
  if (userArchive.value.birthAreaArray.length !== 0) {
    areaAddress.value = getAddress(userArchive.value.birthAreaArray)
  } else {
    areaAddress.value = getAddress2(userArchive.value.birthAreaArray)
  }
}
// 获取显示出生地点
function handleConfirm({ selectedItems }: { selectedItems: { value: string; label: string }[] }) {
  userArchive.value.birthArea = selectedItems.map((item) => item.label).join(' ')
}

// 获取用户基础信息
function getUserInfo() {
  const { data, run } = useRequest<IUserInfoItem>(() => userInfo())
  run().then(() => {
    if (!data.value) return
    const { gender, birthday, birthTime, birthAreaArray, birthArea } = data.value
    const item = {
      gender: gender ? gender.toString() : '',
      birthday: birthday ? dayjs(String(birthday), 'YYYYMMDD').startOf('day').valueOf() : '',
      birthTime: birthTime ? formatToHHmm(birthTime) : birthday && !birthTime ? '00:00' : '',
      birthAreaArray: birthAreaArray ? birthAreaArray.split('-') : [],
      birthArea,
    }
    userArchive.value = item
    onModify()
  })
}
// 补齐时间
function formatToHHmm(num: number | string): string {
  const padded = num.toString().padStart(4, '0')
  const hours = padded.slice(0, 2)
  const minutes = padded.slice(2, 4)
  return `${hours}:${minutes}`
}
// 引入消息框组件
const MessageBoxHbElement = ref<InstanceType<typeof MessageBoxHb> | null>(null)
// 显示消息框
function showMessageBox() {
  if (MessageBoxHbElement.value) {
    MessageBoxHbElement.value.open()
  }
}
// 去测试信息
function goTestInformation() {
  uni.showLoading({
    title: '测算中(约35s)',
    mask: true,
  })
  if (testTarget.value === 'self') {
    const prams: IUserInfoQuery = {
      ...userArchive.value,
      birthday:
        userArchive.value.birthday || typeof userArchive.value.birthday === 'number'
          ? Number(dayjs(userArchive.value.birthday).format('YYYYMMDD'))
          : '',
      birthTime: userArchive.value.birthTime
        ? Number(String(userArchive.value.birthTime).replace(':', ''))
        : '',
      birthAreaArray: userArchive.value.birthAreaArray.length
        ? userArchive.value.birthAreaArray.join('-')
        : '', // 出生地址编码收获地址编码
    }
    const { data, run } = useRequest<IUserInfoItem>(() => userUpdate(prams))
    run().then(() => {
      const userInfo = { nickName: userArchive.value.nickName, gender: userArchive.value.gender }
      userStore.setUserInfo(userInfo)
      getAiChat()
    })
  } else {
    getAiChat()
  }
}

// 开始测试八字
function getAiChat() {
  const params: IAiChatQuery = {
    birthDay:
      userArchive.value.birthday || typeof userArchive.value.birthday === 'number'
        ? Number(dayjs(userArchive.value.birthday).format('YYYYMMDD'))
        : '',
    // 出生时间，格式 HHmm
    birthTime: userArchive.value.birthTime
      ? Number(String(userArchive.value.birthTime).replace(':', ''))
      : '',
    birthArea: userArchive.value.birthArea,
    gender: userArchive.value.gender ? Number(userArchive.value.gender) : '',
    isSelf: testTarget.value === 'self' ? 1 : 0,
  }
  console.log('params', params)
  const { data, code, run, msg: message } = useRequest<IAiChatItem>(() => aiChat(params))
  run().then((res) => {
    uni.hideLoading()
    if (code.value === '0') {
      getTestResult(data.value)
      uni.navigateTo({ url: `/pages/startTesting/index?id=${data.value.id}&isShare=0` })
    } else if (code.value === '1') {
      msg.value = '当前使用人数过多，请稍后重试'
      showMessageBox()
    } else if (code.value === '500') {
      msg.value = '当前已达测试次数限制，无法测试'
      showMessageBox()
    }
  })
}

const onConfirm = ({ value }) => {
  nextTick(() => {
    userArchive.value.birthday = value
  })
}

function changeTestTarget({ value }) {
  testTarget.value = value
  if (value === 'self') {
    getUserInfo()
  } else {
    const item = {
      gender: '',
      birthday: '',
      birthTime: '',
      birthAreaArray: [],
      birthArea: '',
    }
    userArchive.value = item
    onModify()
  }
}
onShow(() => {
  testTarget.value === 'self' && getUserInfo()
})
</script>

<style lang="scss" scoped>
//时间提示文字css开始位置
.txtClass {
  padding-right: 36rpx;
  font-family: SourceHanSerifCN;
  font-size: 27rpx;
  font-style: normal;
  font-weight: 500;
  line-height: 39rpx;
  color: #666666;
  text-align: right;
}
//时间提示文字css结束位置

//穿透样式修改Css开始位置
:deep(.wd-input__inner) {
  height: 76rpx !important;
  padding: 0 20rpx !important;
  text-align: right !important;
}

:deep(.wd-cell__title) {
  font-family: SourceHanSerifCN;
  font-size: 30rpx !important;
  font-weight: bold;
  color: #1e2127;
}

:deep(.wd-picker__label) {
  font-family: SourceHanSerifCN;
  font-size: 30rpx !important;
  font-weight: bold;
  color: #1e2127;
}

:deep(.wd-col-picker__label) {
  font-family: SourceHanSerifCN;
  font-size: 30rpx !important;
  font-weight: bold;
  color: #1e2127;
}

:deep(.wd-picker-view-column__item--active) {
  color: #fc3b3b;
}

:deep(.wd-col-picker__selected-line) {
  background: #fc3b3b !important;
}

:deep() {
  //按钮样式
  .custom {
    width: 100%;
    height: 80rpx;
    background: #e73c3c;
  }
  .custom1 {
    width: 90%;
    height: 80rpx;
  }
  .wd-button.is-medium.is-round {
    min-width: 200rpx !important;
  }
}
//穿透样式修改Css结束位置
</style>
