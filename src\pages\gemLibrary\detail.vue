<route lang="json5">
{
  style: {
    navigationBarTitleText: '宝石详情',
  },
}
</route>

<template>
  <view class="h750rpx card-swiper">
    <image :src="detailData?.picture" mode="aspectFill" />
  </view>
  <view class="m20rpx h282rpx bg-#fff b-rd-20rpx pt-40rpx pb-40rpx pl-20rpx pr-20rpx">
    <view class="flex">
      <view class="flex-1 text-left font-[SourceHanSerifCN] fw-bold text-34rpx">
        {{ detailData?.name }}
      </view>
      <view class="flex-1 text-right fw-bold c-#DD0006 font-[SourceHanSerifCN]">
        <text class="text-24rpx">¥</text>
        <text class="text-34rpx">{{ detailData?.minPrice }}</text>
        <text class="text-24rpx">起</text>
      </view>
    </view>
    <view class="h-80rpx b-rd-13rpx mt-30rpx">
      <image
        :src="getImagesUrl('685ac559e4b00e5b2cf3619b.png')"
        mode="aspectFill"
        v-if="detailData?.attribute === '金'"
      />
      <image
        :src="getImagesUrl('685ac55de4b00e5b2cf3619c.png')"
        mode="aspectFill"
        v-else-if="detailData?.attribute === '木'"
      />
      <image
        :src="getImagesUrl('68596fafe4b00e5b2cf36195.png')"
        mode="aspectFill"
        v-else-if="detailData?.attribute === '水'"
      />
      <image
        :src="getImagesUrl('685ac555e4b00e5b2cf3619a.png')"
        mode="aspectFill"
        v-else-if="detailData?.attribute === '火'"
      />
      <image
        :src="getImagesUrl('685ac561e4b00e5b2cf3619d.png')"
        mode="aspectFill"
        v-else-if="detailData?.attribute === '土'"
      />
    </view>
    <view class="mt-20rpx flex">
      <view
        class="center w-110rpx h-50rpx bg-[rgba(117,117,117,0.07)] b-rd-23rpx mr-10rpx"
        v-if="detailData?.labelList.includes('健康')"
      >
        <image src="../../static/home/<USER>" class="w-30rpx h-25rpx" mode="aspectFill" />
        <view class="font-[SourceHanSerifCN] text-22rpx fw-bold lh-30rpx pl-5rpx c-#027741">
          健康
        </view>
      </view>
      <view
        class="center w-110rpx h-50rpx bg-[rgba(117,117,117,0.07)] b-rd-23rpx mr-10rpx"
        v-if="detailData?.labelList.includes('财富')"
      >
        <image src="../../static/home/<USER>" class="w-30rpx h-25rpx" mode="aspectFill" />
        <view class="font-[SourceHanSerifCN] text-22rpx fw-bold lh-30rpx pl-5rpx c-#e96f00">
          财富
        </view>
      </view>
      <view
        class="center w-110rpx h-50rpx bg-[rgba(117,117,117,0.07)] b-rd-23rpx mr-10rpx"
        v-if="detailData?.labelList.includes('爱情')"
      >
        <image src="../../static/home/<USER>" class="w-30rpx h-25rpx" mode="aspectFill" />
        <view class="font-[SourceHanSerifCN] text-22rpx fw-bold lh-30rpx pl-5rpx c-#fd466f">
          爱情
        </view>
      </view>
      <view
        class="center w-110rpx h-50rpx bg-[rgba(117,117,117,0.07)] b-rd-23rpx mr-10rpx"
        v-if="detailData?.labelList.includes('事业')"
      >
        <image src="../../static/home/<USER>" class="w-30rpx h-25rpx" mode="aspectFill" />
        <view class="font-[SourceHanSerifCN] text-22rpx fw-bold lh-30rpx pl-5rpx c-#6b4023">
          事业
        </view>
      </view>
      <view
        class="center w-110rpx h-50rpx bg-[rgba(117,117,117,0.07)] b-rd-23rpx mr-10rpx"
        v-if="detailData?.labelList.includes('平安')"
      >
        <image src="../../static/home/<USER>" class="w-30rpx h-25rpx" mode="aspectFill" />
        <view class="font-[SourceHanSerifCN] text-22rpx fw-bold lh-30rpx pl-5rpx c-#e32117">
          平安
        </view>
      </view>
    </view>
    <view class="text-25rpx fw-500 lh-43rpx mt-24rpx">{{ detailData?.functionInfo }}</view>
  </view>

  <view class="m20rpx bg-#fff b-rd-20rpx">
    <view class="p-20rpx">
      <view class="h-82rpx pt-10rpx">
        <image :src="getImagesUrl('68596e9fe4b00e5b2cf36194.png')" mode="aspectFill" />
      </view>
      <view class="text-30rpx fw-bold font-[SourceHanSerifCN] lh-44rpx pt-30rpx">详细介绍</view>

      <view class="mt40rpx bg-#fff">
        <view class="flex bg-#f7f7f7 mt3rpx">
          <view class="flex-1 text-center lh-80rpx text-26rpx fw-bold font-[SourceHanSerifCN]">
            直径
          </view>
          <view class="h-70rpx w-2rpx bg-#fff mt-10px"></view>
          <view class="flex-1 text-center lh-80rpx text-26rpx fw-bold font-[SourceHanSerifCN]">
            价格
          </view>
        </view>
        <view class="flex bg-#f7f7f7 mt3rpx" v-for="item in detailData?.skuList" :key="item.id">
          <view class="flex-1 text-center lh-80rpx text-26rpx fw-500 font-[SourceHanSerifCN]">
            {{ item?.diameter }}
          </view>
          <view class="h-70rpx w-2rpx bg-#fff mt-10rpx"></view>
          <view class="flex-1 text-center lh-80rpx text-26rpx fw-500 font-[SourceHanSerifCN]">
            {{ item?.price }}
          </view>
        </view>
      </view>
      <view class="lh-43rpx text-22rpx fw-500 font-[SourceHanSerifCN] c-#999999 mt-20rpx">
        *手工测量存在误差，价格仅提供参考，最终以人工客服提供为准
      </view>
      <view class="flex mt-20rpx">
        <view class="w-70rpx lh-43rpx text-25rpx fw-500 font-[SourceHanSerifCN] c-#666666">
          材质
        </view>
        <view class="flex-1 lh-43rpx text-25rpx fw-500 font-[SourceHanSerifCN] c-#000000">
          {{ detailData?.material }}
        </view>
      </view>
      <view class="flex mt-20rpx">
        <view class="w-70rpx lh-43rpx text-25rpx fw-500 font-[SourceHanSerifCN] c-#666666">
          产地
        </view>
        <view class="flex-1 lh-43rpx text-25rpx fw-500 font-[SourceHanSerifCN] c-#000000">
          {{ detailData?.origin }}
        </view>
      </view>
      <view class="flex mt-20rpx">
        <view class="w-70rpx lh-43rpx text-25rpx fw-500 font-[SourceHanSerifCN] c-#666666">
          其他
        </view>
        <view class="flex-1 lh-43rpx text-25rpx fw-500 font-[SourceHanSerifCN] c-#000000">
          {{ detailData?.otherInfo }}
        </view>
      </view>
      <mp-html :content="detailData?.moreInfo" />
    </view>
    <view class="h-30rpx"></view>
  </view>
  <view class="productDetailsButtonBodyPlaceholderClass"></view>
  <view class="productDetailsButtonBodyClass">
    <view class="productDetailsButtonBodyInnerClass">
      <button class="productDetailsButtonBodyInnerLeftClass" open-type="share">
        <view class="productDetailsButtonBodyInnerLeftShareClass">
          <image src="../../static/images/shareIcon.png" mode="aspectFill" />
          <view class="productDetailsButtonBodyInnerLeftShareTextClass">分享</view>
        </view>
      </button>
      <view class="productDetailsButtonBodyInnerRightClass" @click="showMessageBox">
        <wd-button custom-class="custom">
          <view class="i-carbon-headset pb-8rpx" />
          <text class="pl-20rpx">联系客服</text>
        </wd-button>
      </view>
    </view>
    <CustomerService ref="CustomerServiceElement" />
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { getImagesUrl } from '@/utils/getImagesUrl'
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
import CustomerService from '@/components/CustomerService.vue'
import { IGemDetailItem, gemDetail } from '@/service/gemLibrary'
import { getMinNum } from '@/utils/getMinNum'
defineOptions({
  name: 'detail',
})

const detailId = ref('')
onLoad((options) => {
  const { id } = options
  detailId.value = id
  getGemList(id)
})

// 分享
onShareAppMessage(() => {
  return {
    title: detailData.value?.functionInfo,
    path: `pages/gemLibrary/detail?id=${detailId.value}`,
  }
})

// 轮播图值

const detailData = ref(null)

// 获取宝石标签
function getGemList(id: string) {
  const { data, run } = useRequest<IGemDetailItem>(() => gemDetail(id))
  run().then(() => {
    if (!data.value.length) {
      const minPrice = getMinNum(data.value.skuList, 'price', null)
      const material = data.value.typeList.filter((e) => e.type === 2)[0].typeName
      const attribute = data.value.typeList.filter((e) => e.type === 0)[0].typeName
      detailData.value = { ...data.value, minPrice, material, attribute }
    }
  })
}

const CustomerServiceElement = ref(null)

// 显示消息框
function showMessageBox() {
  if (CustomerServiceElement.value) {
    CustomerServiceElement.value.open()
  }
}
</script>

<style lang="scss" scoped>
.card-swiper {
  --wot-swiper-nav-dot-color: rgba(0, 0, 0, 0.3);
  --wot-swiper-nav-dot-active-color: #000000;
  :deep(.wd-swiper__track) {
    height: 750rpx !important;
    border-radius: 0rpx;
  }
  :deep(.wd-swiper__image) {
    height: 750rpx !important;
  }
  :deep(.wd-swiper__item) {
    height: 750rpx !important;
  }
}

//商品底部按钮开始位置
.productDetailsButtonBodyPlaceholderClass {
  height: 188rpx;
}
.productDetailsButtonBodyClass {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  height: 188rpx;
  background: #fff;
  .productDetailsButtonBodyInnerClass {
    display: flex;
    width: 100%;
    height: 100%;
    .productDetailsButtonBodyInnerLeftClass {
      display: flex;
      flex: 2;
      justify-content: center; // 水平居中
      background-color: #fff; // 可选，调试用;
      .productDetailsButtonBodyInnerLeftShareClass {
        width: 44rpx;
        height: 44rpx;
        // margin-top: 29rpx;
      }
      .productDetailsButtonBodyInnerLeftShareTextClass {
        font-family: SourceHanSerifCN;
        font-size: 20rpx;
        font-style: normal;
        font-weight: bold;
        line-height: 0;
        color: #1e2127;
        text-align: right;
      }
    }
    .productDetailsButtonBodyInnerRightClass {
      flex: 8;
      :deep() {
        //按钮样式
        .custom {
          width: 600rpx;
          height: 81rpx;
          margin: 20rpx 27rpx 0 0;
          background: #e73c3c;
        }
        //按钮圆角
        .wd-button.is-round {
          border-radius: 13rpx;
        }
        //按钮文字样式
        .wd-button__text {
          font-size: 32rpx;
          font-weight: bold;
          color: #fff9f3;
        }
      }
    }
  }
  //商品底部按钮结束位置
}
</style>
