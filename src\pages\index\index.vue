<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: '沁-新中式珠宝',
  },
}
</route>
<template>
  <view v-if="!loadingIndex">
    <!-- 轮播图开始位置 -->
    <view class="swiperClass">
      <swiper
        class="swiper-box"
        :current="current"
        indicator-dots
        indicator-color="rgba(0, 0, 0, 0.1)"
        indicator-active-color="#FFF"
        autoplay
      >
        <swiper-item v-for="(item, index) in swiperList" :key="index" @click="handleClick(item)">
          <image :src="item.picture" mode="aspectFill" />
        </swiper-item>
      </swiper>
      <!-- <wd-swiper
        :list="swiperList"
        autoplay
        height="350rpx"
        v-model:current="current"
        @click="handleClick"
        value-key="picture"
        :display-multiple-items="0"
      ></wd-swiper> -->
    </view>
    <!-- 轮播图结束位置 -->

    <!-- 测八字开始位置 -->
    <view class="baziCalculatorClass">
      <image :src="baziCalculatorImgLs[0]?.picture" mode="aspectFill" />
      <view class="baziCalculatorButtonClass">
        <wd-button custom-class="custom" @click="goTestEightCharacters">马上测试</wd-button>
      </view>
    </view>
    <!-- 测八字结束位置 -->

    <!-- 开运吉品开始位置 -->
    <view class="titleClass">
      <image src="../../static/home/<USER>" mode="aspectFill" />
      <text class="titleTextClass">开运吉品</text>
    </view>
    <!-- 开运吉品结束位置 -->

    <!-- 切换标签开始位置 -->
    <view class="tabClass">
      <TabSelector :tabs="tabs" @change="changeTab"></TabSelector>
    </view>
    <!-- 切换标签结束位置 -->
    <!-- 商品展示开始位置 -->
    <view class="m-9rpx">
      <wd-row>
        <wd-col :span="12" v-for="item in goodsListData" :key="item.id">
          <view
            class="goodsItemClass"
            hover-class="goodsItemHover"
            :hover-stay-time="150"
            @click="goToProductDetails(item)"
          >
            <image :src="item.picture" class="goodsImagesClass" mode="aspectFill" />
            <view class="goodsDescribeClass">{{ item.name }}</view>
            <view class="goodsTextClass">
              <wd-row>
                <wd-col :span="8">
                  <view class="goodsPriceClass">
                    ¥
                    <text class="text-32rpx">{{ item.price }}</text>
                  </view>
                </wd-col>
                <wd-col :span="16">
                  <view class="flex justify-end flex-wrap mt-4rpx">
                    <view class="goodsTypeClass ml-5rpx" v-if="item.labels.includes('健康')">
                      <image
                        src="../../static/home/<USER>"
                        class="goodsTypeImagesClass"
                        mode="aspectFill"
                      />
                      <view class="goodsTypeTextClass" style="color: #027741">健康</view>
                    </view>
                    <view class="goodsTypeClass ml-5rpx" v-if="item.labels.includes('财富')">
                      <image
                        src="../../static/home/<USER>"
                        class="goodsTypeImagesClass"
                        mode="aspectFill"
                      />
                      <view class="goodsTypeTextClass" style="color: #e96f00">财富</view>
                    </view>
                    <view class="goodsTypeClass ml-5rpx" v-if="item.labels.includes('爱情')">
                      <image
                        src="../../static/home/<USER>"
                        class="goodsTypeImagesClass"
                        mode="aspectFill"
                      />
                      <view class="goodsTypeTextClass" style="color: #fd466f">爱情</view>
                    </view>
                    <view class="goodsTypeClass ml-5rpx" v-if="item.labels.includes('事业')">
                      <image
                        src="../../static/home/<USER>"
                        class="goodsTypeImagesClass"
                        mode="aspectFill"
                      />
                      <view class="goodsTypeTextClass" style="color: #6b4023">事业</view>
                    </view>
                    <view class="goodsTypeClass ml-5rpx" v-if="item.labels.includes('平安')">
                      <image
                        src="../../static/home/<USER>"
                        class="goodsTypeImagesClass"
                        mode="aspectFill"
                      />
                      <view class="goodsTypeTextClass" style="color: #e32117">平安</view>
                    </view>
                  </view>
                </wd-col>
              </wd-row>
            </view>
          </view>
        </wd-col>
      </wd-row>
      <wd-loadmore
        :state="state"
        @reload="loadmore"
        :loading-props="{ color: '#e73c3c', size: 20 }"
      />
    </view>
    <!-- 商品展示结束位置 -->
  </view>
</template>

<script lang="ts" setup>
import TabSelector from '@/components/TabSelector.vue'
import { onReachBottom } from '@dcloudio/uni-app'

import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { useLoginGuard } from '@/composables/useTheme'
import { getBannerList } from '@/utils/getBannerList'
import {
  IBannerItem,
  luckyTypeList,
  ILuckyTypeListItem,
  ITabsListItem,
  ILuckyPageQuery,
  luckyPage,
  ILuckyPageItem,
} from '@/service/index/index'
import { ref, onBeforeMount } from 'vue'
const { checkLogin } = useLoginGuard()
defineOptions({
  name: 'Home',
})
// 轮播图值
const current = ref<number>(null)
// 切换标签数据
const tabs = ref<ITabsListItem[]>([])
// 页数
const pages = ref<number>(1)
const loadingIndex = ref(true)

// 轮播图数据来源
const swiperList = ref<IBannerItem[]>([])
const baziCalculatorImgLs = ref<IBannerItem[]>([])
// 商品列表
const goodsListData = ref([])
const state = ref<LoadMoreState>('finished')
const total = ref(0)
const typeId = ref(null)

// 轮播图点击事件
function handleClick(item: any) {
  const { setData } = useData()
  setData('curbannerData', item)
  uni.navigateTo({
    url: `/pages/customizedJewelry/index`,
  })
}
// 获取切换标签数据
async function getLuckyTypeList() {
  const { data, run } = useRequest<ILuckyTypeListItem[]>(() => luckyTypeList())
  run().then(() => {
    tabs.value = data.value
    typeId.value = data.value[0].id
    getLuckyPage(typeId.value)
  })
}
// tab切换事件
function changeTab(item: ITabsListItem, index: number) {
  const { id } = item
  typeId.value = id
  pages.value = 1
  total.value = 0
  goodsListData.value = []
  state.value = 'finished'
  getLuckyPage(id)
}
// 页面加载时加载数据
onReachBottom(() => {
  if (goodsListData.value.length < total.value) {
    loadmore()
  } else {
    state.value = 'finished'
  }
})

// 加载更多数据
async function loadmore() {
  await getLuckyPage(typeId.value)
  if (goodsListData.value.length >= total.value) {
    state.value = 'finished'
  } else {
    state.value = 'loading'
  }
}
// 获取商品详情
function getLuckyPage(id: string) {
  const parameter: ILuckyPageQuery = {
    page: pages.value,
    typeId: id,
    size: 10,
  }
  const { data, run } = useRequest<ILuckyPageItem>(() => luckyPage(parameter))
  run().then(() => {
    pages.value++
    const list = data.value.list.map((e) => {
      return {
        ...e,
        labels: e.labels.length <= 2 ? e.labels : e.labels.slice(0, 2),
      }
    })
    goodsListData.value = [...goodsListData.value, ...list]
    total.value = data.value.total
  })
}
// 去测试八字
function goTestEightCharacters() {
  if (!checkLogin()) return
  uni.navigateTo({
    url: `/pages/testEightCharacters/index`,
  })
}

// 跳转到商品详情
function goToProductDetails(item: { id: number }) {
  if (!checkLogin()) return
  uni.navigateTo({
    url: `/pages/productDetails/index?id=${item.id}`,
  })
}

onBeforeMount(async () => {
  uni.showLoading({
    title: '加载中',
  })
  swiperList.value = await getBannerList('1')
  baziCalculatorImgLs.value = await getBannerList('2')
  await getLuckyTypeList()
  loadingIndex.value = false
  uni.hideLoading()
})
</script>

<style lang="scss" scoped>
//轮播图css开始位置
.swiperClass {
  width: 100%;
  height: 350rpx;
  :deep(.wd-swiper__track) {
    border-radius: 0rpx;
  }
}
//轮播图css结束位置

//测八字css开始位置
//测八字占位区域
.baziCalculatorClass {
  position: relative;
  width: 699rpx;
  height: 304rpx;
  margin: 30rpx auto 0 auto;
}
//测八字按钮占位
.baziCalculatorButtonClass {
  position: absolute;
  top: 198rpx;
  left: 48rpx;
  :deep() {
    //按钮样式
    .custom {
      width: 423.59rpx;
      height: 79.79rpx;
      background: #e73c3c;
    }
    //按钮圆角
    .wd-button.is-round {
      border-radius: 15rpx;
    }
    //按钮文字样式
    .wd-button__text {
      font-size: 34rpx;
      font-weight: bold;
      color: #fff3e4;
    }
  }
}
//测八字css结束位置

//开运吉品css开始位置
.titleClass {
  position: relative;
  width: 271rpx;
  height: 30rpx;
  margin: 40rpx auto 0 auto;
}
.titleTextClass {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 33rpx;
  font-weight: bold;
  color: #e73c3c;
  transform: translate(-50%, -35%);
}
//开运吉品css结束位置

//标签栏css开始位置
.tabClass {
  margin-top: 40rpx;
}
//标签栏css结束位置
//商品展示位css开始位置
.goodsItemClass {
  height: 510rpx;
  margin: 6rpx;
  background: #ffffff;
  border-radius: 20rpx;
  .goodsImagesClass {
    height: 354rpx;
    background: #f8eded;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
  }
  .goodsDescribeClass {
    width: 311rpx;
    height: 64rpx;
    margin: 20rpx auto 0 auto;
    font-size: 25rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 32rpx;
    color: #000000;
    text-align: left;
  }
  .goodsTextClass {
    width: 311rpx;
    margin: 15rpx auto 0 auto;
    .goodsPriceClass {
      font-family: SourceHanSerifCN;
      font-size: 29rpx;
      font-style: normal;
      font-weight: bold;
      line-height: 41rpx;
      color: #fc3b3b;
      text-align: left;
    }
    .goodsTypeClass {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 90rpx;
      height: 35rpx;
      background: rgba(117, 117, 117, 0.07);
      border-radius: 18rpx;
      .goodsTypeImagesClass {
        width: 23rpx;
        height: 18rpx;
      }
      .goodsTypeTextClass {
        font-family: SourceHanSerifCN;
        font-size: 22rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 22rpx;
      }
    }
  }
}
.goodsItemHover {
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1); // 添加阴影效果
  transform: scale(0.97); // 点击时缩小一点
}
//商品展示位css结束位置
</style>
