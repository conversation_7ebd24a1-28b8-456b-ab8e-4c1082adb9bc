import { http } from '@/utils/http'

// 宝石标签类型
export interface IGemTypeListItem {
  id: string
  typeName: string
  ordered: number
}

export interface IListItem {
  id: string
  name: string
  picture: string
}

// 宝石标签类型
export interface IGemEnjoyColorItem {
  id: string
  name: string
  list: IListItem[]
}
// 单个 SKU 类型
interface SkuItem {
  id: number
  diameter: number
  price: number
}

// 主商品类型
export interface IGemDetailItem {
  id: string
  name: string
  picture: string
  origin: string
  otherInfo: string
  functionInfo: string
  moreInfo: string
  labelList: string[]
  skuList: SkuItem[]
  sku: SkuItem
  typeList: Array<{
    type: number
    typeName: string
  }>
}

/** GET 请求 获取宝石标签 */
export const gemTypeList = (type) => {
  return http.get<IGemTypeListItem[]>(`/gem/${type}/list`)
}

/** GET 请求 我的喜用 */
export const gemEnjoyColor = () => {
  return http.get<IGemEnjoyColorItem[]>('/gem/enjoyColor')
}

/** GET 请求 查询宝石 */
export const gemList = (data: { type: string; colorId?: string | number }) => {
  return http.get<IGemEnjoyColorItem[]>('/gem/list', { ...data })
}

/** GET 请求 查询宝石详情 */
export const gemDetail = (id: string) => {
  return http.get<IGemDetailItem>(`/gem/detail/${id}`)
}

/** GET 请求 查询宝石详情 */
export const gemSkuDetail = (id: string) => {
  return http.get<IGemDetailItem[]>(`/gem/sku/detail/${id}`)
}

// Canvas渲染相关类型定义
export interface BeadInfo {
  id: number
  name: string
  image?: string
  imagePath?: string
  size: number
  materialName: string
  price: number
  skuId: number
  uniqueId: number
  color?: string
  isEmptyHole?: boolean
  removedBead?: any
}

export interface BeadPositionInfo {
  centerX: number
  centerY: number
  beadSize: number
  angle: number
  index: number
}

export interface CanvasState {
  beads: BeadInfo[]
  beadInfoArray: BeadPositionInfo[]
  actualRadius: number | null
  scale: number
  rotation: number
  translateY: number
  transformOrigin: { x: number; y: number }
  totalBeads: number
  maxBeads: number
  selectedIndex: number
  isProcessingDrawTasks: boolean
  canvasReady: boolean
}

export interface SummaryInfo {
  totalCount: number
  circumference: number
  recommendedGender: string
  gender: number
  totalCost: number
  handworkFee: number
  beadDetails: any[]
}

// Canvas渲染器类
export class BraceletCanvasRenderer {
  private ctx: CanvasRenderingContext2D | null = null
  private canvasEl: any = null
  private state: CanvasState
  private summaryInfo: SummaryInfo

  constructor() {
    this.state = {
      beads: [],
      beadInfoArray: [],
      actualRadius: null,
      scale: 1,
      rotation: 0,
      translateY: 0,
      transformOrigin: { x: 150, y: 150 },
      totalBeads: 18,
      maxBeads: 40,
      selectedIndex: -1, // 只渲染模式下不需要选中功能
      isProcessingDrawTasks: false,
      canvasReady: false,
    }

    this.summaryInfo = {
      totalCount: 0,
      circumference: 0,
      recommendedGender: '',
      gender: 0,
      totalCost: 0,
      handworkFee: 50,
      beadDetails: [],
    }
  }

  // 初始化Canvas
  initCanvas(canvasId: string, width: number, height: number): Promise<boolean> {
    return new Promise((resolve) => {
      setTimeout(() => {
        try {
          const query = uni.createSelectorQuery()
          query
            .select(`#${canvasId}`)
            .fields({ node: true, size: true }, (res: any) => {
              if (res && res.node) {
                const canvas = res.node
                this.canvasEl = { canvas, node: canvas, width, height }
                this.ctx = canvas.getContext('2d')

                // 设置Canvas尺寸
                canvas.width = width
                canvas.height = height

                if (!this.ctx) {
                  console.error('无法获取Canvas 2D上下文')
                  resolve(false)
                  return
                }

                this.state.transformOrigin = {
                  x: width / 2,
                  y: height / 2,
                }

                this.state.canvasReady = true
                console.log('Canvas初始化成功', { width, height })
                resolve(true)
              } else {
                console.error('无法获取Canvas节点', res)
                resolve(false)
              }
            })
            .exec()
        } catch (error) {
          console.error('Canvas初始化出错:', error)
          resolve(false)
        }
      }, 100)
    })
  }

  // 设置珠子数据
  setBeads(beads: BeadInfo[], summaryInfo?: SummaryInfo) {
    this.state.beads = beads || []
    if (summaryInfo) {
      this.summaryInfo = summaryInfo
    }

    // 重新计算珠子位置
    this.recalculateBeadPositions()
  }

  // 渲染手串
  render() {
    if (!this.ctx || !this.canvasEl || !this.state.canvasReady) {
      console.error('Canvas未准备好，跳过渲染')
      return
    }

    this.drawBracelet()
  }

  // 主绘制函数
  private drawBracelet() {
    if (!this.ctx || !this.canvasEl) {
      console.error('Canvas上下文不存在')
      return
    }

    console.log('开始绘制手串，珠子数量:', this.state.beads.length)

    // 如果没有珠子位置信息，重新计算
    if (!this.state.beadInfoArray || this.state.beadInfoArray.length === 0) {
      console.log('没有珠子位置信息，重新计算')
      if (this.state.beads.length <= 3 && this.state.beads.length > 0) {
        console.log('使用特殊布局')
        this.applySpecialLayout(this.state.beads.length)
      } else if (this.state.beads.length > 0) {
        console.log('使用圆形布局')
        this.recalculateBeadPositions()
      }
    }

    // 清空画布
    this.ctx.clearRect(0, 0, this.canvasEl.width, this.canvasEl.height)

    // 设置白色背景用于测试
    this.ctx.fillStyle = 'rgba(255, 255, 255, 1.0)'
    this.ctx.fillRect(0, 0, this.canvasEl.width, this.canvasEl.height)

    // 绘制一个测试圆圈确保Canvas工作正常
    const centerX = this.state.transformOrigin.x
    const centerY = this.state.transformOrigin.y
    this.ctx.beginPath()
    this.ctx.arc(centerX, centerY, 5, 0, Math.PI * 2)
    this.ctx.fillStyle = 'red'
    this.ctx.fill()
    console.log('测试圆圈已绘制在中心:', centerX, centerY)

    // 绘制手串
    const radius =
      this.state.actualRadius || Math.min(this.canvasEl.width, this.canvasEl.height) * 0.3

    console.log('绘制参数:', {
      centerX,
      centerY,
      radius,
      canvasWidth: this.canvasEl.width,
      canvasHeight: this.canvasEl.height,
      beadCount: this.state.beads.length,
      beadInfoCount: this.state.beadInfoArray?.length || 0,
    })

    this.ctx.save()

    // 应用变换（缩放、旋转、平移）
    this.ctx.translate(centerX, centerY + this.state.translateY)
    this.ctx.rotate(this.state.rotation)
    this.ctx.scale(this.state.scale, this.state.scale)
    this.ctx.translate(-centerX, -centerY)

    // 绘制珠子
    this.drawBeadHoles(centerX, centerY, radius)

    this.ctx.restore()

    console.log('手串绘制完成')
  }

  // 重新计算珠子位置
  private recalculateBeadPositions() {
    if (!this.canvasEl || !this.ctx) {
      console.error('Canvas未初始化，无法重新计算珠子位置')
      return
    }

    const totalBeadCount = this.state.beads.length
    console.log('重新计算珠子位置，珠子数量:', totalBeadCount)

    if (totalBeadCount === 0) {
      this.state.beadInfoArray = []
      return
    }

    const centerX = this.state.transformOrigin.x
    const centerY = this.state.transformOrigin.y

    // 简化半径计算
    const radius = Math.min(this.canvasEl.width, this.canvasEl.height) * 0.3
    this.state.actualRadius = radius

    console.log('计算参数:', {
      centerX,
      centerY,
      radius,
      canvasWidth: this.canvasEl.width,
      canvasHeight: this.canvasEl.height,
    })

    // 计算每个珠子的位置 - 使用简单的等分圆周方式
    const beadInfoArray: BeadPositionInfo[] = []
    const gapAngle = (Math.PI / 180) * 2 // 2度间隔
    const totalAngleForBeads = 2 * Math.PI - gapAngle * totalBeadCount
    const anglePerBead = totalAngleForBeads / totalBeadCount

    let currentAngle = -Math.PI / 2 // 从顶部开始

    for (let i = 0; i < totalBeadCount; i++) {
      const bead = this.state.beads[i]
      if (!bead) continue

      const beadSize = Math.max((bead.size || 12) * 0.8, 15) // 最小15px
      const x = centerX + radius * Math.cos(currentAngle)
      const y = centerY + radius * Math.sin(currentAngle)

      beadInfoArray.push({
        centerX: x,
        centerY: y,
        beadSize,
        angle: currentAngle,
        index: i,
      })

      console.log(
        `珠子${i}: 位置(${x.toFixed(1)}, ${y.toFixed(1)}), 大小:${beadSize}, 角度:${((currentAngle * 180) / Math.PI).toFixed(1)}度`,
      )

      currentAngle += anglePerBead + gapAngle
    }

    this.state.beadInfoArray = beadInfoArray
    console.log('珠子位置计算完成，总数:', beadInfoArray.length)
  }

  // 特殊布局（3颗及以下珠子）
  private applySpecialLayout(beadCount: number) {
    if (!this.canvasEl) return

    const centerX = this.state.transformOrigin.x
    const centerY = this.state.transformOrigin.y
    const baseSize = 25
    const beadInfoArray: BeadPositionInfo[] = []

    switch (beadCount) {
      case 1:
        beadInfoArray.push({
          centerX,
          centerY,
          beadSize: baseSize,
          angle: 0,
          index: 0,
        })
        break

      case 2:
        const spacing2 = baseSize * 2.2
        beadInfoArray.push(
          {
            centerX: centerX - spacing2 / 2,
            centerY,
            beadSize: baseSize,
            angle: Math.PI,
            index: 0,
          },
          {
            centerX: centerX + spacing2 / 2,
            centerY,
            beadSize: baseSize,
            angle: 0,
            index: 1,
          },
        )
        break

      case 3:
        const radius3 = baseSize * 1.5
        for (let i = 0; i < 3; i++) {
          const angle = (i * 2 * Math.PI) / 3 - Math.PI / 2
          beadInfoArray.push({
            centerX: centerX + radius3 * Math.cos(angle),
            centerY: centerY + radius3 * Math.sin(angle),
            beadSize: baseSize,
            angle,
            index: i,
          })
        }
        break

      default:
        // 大于3颗珠子使用正常布局
        this.recalculateBeadPositions()
        return
    }

    this.state.beadInfoArray = beadInfoArray
  }

  // 绘制珠子
  private drawBeadHoles(centerX: number, centerY: number, radius: number) {
    if (!this.ctx) {
      console.error('Canvas上下文不存在')
      return
    }

    const beadInfoArray = this.state.beadInfoArray || []
    console.log(
      '开始绘制珠子，位置信息数量:',
      beadInfoArray.length,
      '珠子数据数量:',
      this.state.beads.length,
    )

    if (beadInfoArray.length === 0 && this.state.beads.length > 0) {
      console.error('警告：有珠子数据但没有珠子位置信息，重新计算位置')
      this.recalculateBeadPositions()
      return
    }

    const drawTasks: any[] = []

    for (const info of beadInfoArray) {
      const currentBead = this.state.beads[info.index]

      if (!currentBead) {
        console.warn(`珠子索引${info.index}没有对应的珠子数据`)
        continue
      }

      const x = info.centerX
      const y = info.centerY
      const beadSize = info.beadSize

      console.log(
        `绘制珠子${info.index}: 位置(${x}, ${y}), 大小:${beadSize}, 名称:${currentBead.name}`,
      )

      // 绘制珠子背景
      if (currentBead && !currentBead.isEmptyHole) {
        // 先绘制白色背景
        this.ctx.beginPath()
        this.ctx.arc(x, y, beadSize, 0, Math.PI * 2)
        this.ctx.fillStyle = '#ffffff'
        this.ctx.fill()

        // 绘制边框
        this.ctx.beginPath()
        this.ctx.arc(x, y, beadSize, 0, Math.PI * 2)
        this.ctx.strokeStyle = '#ddd'
        this.ctx.lineWidth = 1
        this.ctx.stroke()

        // 如果有图片，添加到绘制任务队列
        if (currentBead.imagePath && this.canvasEl && this.canvasEl.canvas) {
          console.log(`珠子${info.index}有图片，添加到绘制队列:`, currentBead.imagePath)
          drawTasks.push({
            type: 'bead',
            x,
            y,
            beadSize,
            bead: currentBead,
            index: info.index,
          })
        } else {
          // 没有图片，绘制颜色
          this.ctx.beginPath()
          this.ctx.arc(x, y, beadSize * 0.9, 0, Math.PI * 2)
          this.ctx.fillStyle = currentBead.color || '#4FC3F7'
          this.ctx.fill()

          // 绘制珠子名称
          this.ctx.fillStyle = '#333'
          this.ctx.font = '10px sans-serif'
          this.ctx.textAlign = 'center'
          this.ctx.textBaseline = 'middle'
          this.ctx.fillText(currentBead.name || '珠子', x, y)

          console.log(`珠子${info.index}绘制完成（颜色模式）`)
        }
      } else {
        // 绘制空孔位
        const clipRatio = 0.95
        this.ctx.beginPath()
        this.ctx.arc(x, y, beadSize * clipRatio, 0, Math.PI * 2)
        this.ctx.fillStyle = '#f5f5f5'
        this.ctx.fill()

        this.ctx.beginPath()
        this.ctx.lineWidth = 1
        this.ctx.arc(x, y, beadSize * clipRatio, 0, Math.PI * 2)
        this.ctx.strokeStyle = '#dddddd'
        this.ctx.stroke()

        console.log(`珠子${info.index}绘制完成（空孔位模式）`)
      }
    }

    // 如果没有珠子，显示提示文字
    if (beadInfoArray.length === 0) {
      console.log('没有珠子数据，显示提示文字')
      this.ctx.font = '14px sans-serif'
      this.ctx.fillStyle = '#999999'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText('暂无珠子数据', centerX, centerY)
    }

    // 执行图片绘制任务
    if (drawTasks.length > 0) {
      console.log('开始执行图片绘制任务，数量:', drawTasks.length)
      this.executeDrawTasks(drawTasks)
    } else {
      console.log('没有图片绘制任务')
    }
  }

  // 执行绘制任务（处理图片加载）
  private executeDrawTasks(drawTasks: any[]) {
    if (this.state.isProcessingDrawTasks || !this.ctx || !this.canvasEl) return

    this.state.isProcessingDrawTasks = true

    let tasksCompleted = 0
    const totalTasks = drawTasks.length

    const taskCompleted = () => {
      tasksCompleted++
      if (tasksCompleted >= totalTasks) {
        this.state.isProcessingDrawTasks = false
      }
    }

    for (const task of drawTasks) {
      if (task.type === 'bead' && task.bead.imagePath) {
        this.drawBeadImage(task, taskCompleted)
      } else {
        taskCompleted()
      }
    }
  }

  // 绘制珠子图片
  private drawBeadImage(task: any, callback: () => void) {
    if (!this.ctx || !this.canvasEl) {
      callback()
      return
    }

    const img = this.canvasEl.canvas.createImage()

    img.onload = () => {
      if (!this.ctx) {
        callback()
        return
      }

      this.ctx.save()

      // 创建圆形裁剪区域
      this.ctx.beginPath()
      this.ctx.arc(task.x, task.y, task.beadSize, 0, Math.PI * 2)
      this.ctx.clip()

      // 绘制图片
      this.ctx.drawImage(
        img,
        task.x - task.beadSize,
        task.y - task.beadSize,
        task.beadSize * 2,
        task.beadSize * 2,
      )

      this.ctx.restore()
      callback()
    }

    img.onerror = () => {
      // 图片加载失败，绘制颜色圆圈
      if (this.ctx) {
        this.ctx.beginPath()
        this.ctx.arc(task.x, task.y, task.beadSize * 0.95, 0, Math.PI * 2)
        this.ctx.fillStyle = task.bead.color || '#f0f0f0'
        this.ctx.fill()
      }
      callback()
    }

    img.src = task.bead.imagePath
  }

  // 绘制汇总信息气泡（可选功能）
  private drawSummaryBubbles() {
    if (!this.ctx || !this.canvasEl || this.summaryInfo.totalCount === 0) return

    const textArray = [
      `${this.summaryInfo.totalCount}颗 周长${this.summaryInfo.circumference}cm 推荐${this.summaryInfo.recommendedGender}佩戴 ￥${this.summaryInfo.totalCost.toFixed(2)}`,
    ]

    this.drawPriceBubbles(textArray)
  }

  // 绘制价格气泡
  private drawPriceBubbles(textArray: string[]) {
    if (!this.ctx || !this.canvasEl || !textArray || textArray.length === 0) return

    const bubbleHeight = 35
    const bubblePadding = 15
    const bubbleSpacing = 10
    const cornerRadius = 18

    let totalBubbleHeight = 0
    const bubbleWidths: number[] = []

    // 计算每个气泡的宽度和总高度
    this.ctx.font = '12px sans-serif'
    for (const text of textArray) {
      const textWidth = this.ctx.measureText(text).width
      const bubbleWidth = textWidth + bubblePadding * 2
      bubbleWidths.push(bubbleWidth)
      totalBubbleHeight += bubbleHeight + bubbleSpacing
    }
    totalBubbleHeight -= bubbleSpacing // 移除最后一个间距

    // 计算起始位置
    const startY = 20
    let currentY = startY

    // 绘制每个气泡
    for (let i = 0; i < textArray.length; i++) {
      const text = textArray[i]
      const bubbleWidth = bubbleWidths[i]
      const bubbleX = (this.canvasEl.width - bubbleWidth) / 2

      // 绘制气泡背景
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
      this.ctx.beginPath()
      this.ctx.roundRect(bubbleX, currentY, bubbleWidth, bubbleHeight, cornerRadius)
      this.ctx.fill()

      // 绘制文字
      this.ctx.fillStyle = 'white'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText(text, bubbleX + bubbleWidth / 2, currentY + bubbleHeight / 2)

      currentY += bubbleHeight + bubbleSpacing
    }
  }

  // 获取Canvas数据URL（用于导出图片）
  getCanvasDataURL(fileType: string = 'png', quality: number = 1): string | null {
    if (!this.canvasEl || !this.canvasEl.canvas) {
      console.error('Canvas未初始化')
      return null
    }

    try {
      return this.canvasEl.canvas.toDataURL(`image/${fileType}`, quality)
    } catch (error) {
      console.error('获取Canvas数据失败:', error)
      return null
    }
  }

  // 清理资源
  destroy() {
    this.ctx = null
    this.canvasEl = null
    this.state.beads = []
    this.state.beadInfoArray = []
    this.state.canvasReady = false
  }
}

// 便捷函数：创建并初始化渲染器
export async function createBraceletRenderer(
  canvasId: string,
  width: number,
  height: number,
): Promise<BraceletCanvasRenderer | null> {
  const renderer = new BraceletCanvasRenderer()
  const success = await renderer.initCanvas(canvasId, width, height)

  if (success) {
    console.log('手串渲染器创建成功')
    return renderer
  } else {
    console.error('手串渲染器创建失败')
    return null
  }
}

// 便捷函数：渲染测试数据
export function renderTestBracelet(renderer: BraceletCanvasRenderer) {
  const testBeads: BeadInfo[] = [
    {
      id: 1,
      name: '红色珠子',
      size: 12,
      materialName: '测试材质',
      price: 10,
      skuId: 1,
      uniqueId: Date.now(),
      color: '#FF0000',
    },
    {
      id: 2,
      name: '绿色珠子',
      size: 10,
      materialName: '测试材质',
      price: 15,
      skuId: 2,
      uniqueId: Date.now() + 1,
      color: '#00FF00',
    },
    {
      id: 3,
      name: '蓝色珠子',
      size: 14,
      materialName: '测试材质',
      price: 20,
      skuId: 3,
      uniqueId: Date.now() + 2,
      color: '#0000FF',
    },
    {
      id: 4,
      name: '黄色珠子',
      size: 8,
      materialName: '测试材质',
      price: 12,
      skuId: 4,
      uniqueId: Date.now() + 3,
      color: '#FFFF00',
    },
    {
      id: 5,
      name: '紫色珠子',
      size: 16,
      materialName: '测试材质',
      price: 25,
      skuId: 5,
      uniqueId: Date.now() + 4,
      color: '#FF00FF',
    },
  ]

  const testSummary: SummaryInfo = {
    totalCount: testBeads.length,
    circumference: 15.5,
    recommendedGender: '男性',
    gender: 1,
    totalCost: 82,
    handworkFee: 50,
    beadDetails: [],
  }

  console.log('设置测试数据:', testBeads)
  renderer.setBeads(testBeads, testSummary)
  renderer.render()
}
