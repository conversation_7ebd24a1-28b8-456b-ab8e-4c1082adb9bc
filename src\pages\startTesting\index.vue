<route lang="json5">
{
  style: {
    navigationBarTitleText: '命理定制',
    navigationStyle: 'custom',
    disableScroll: true,
  },
}
</route>

<template>
  <view class="wraper">
    <view
      class="overflow-hidden flex items-center justify-center topTitle bg-#FCF3F3 h-110rpx"
      :style="{ paddingTop: safeAreaInsets?.top + 'px' }"
    >
      <view class="i-carbon-chevron-left c-#000 w-100rpx text-47rpx font-bold" @click="goBack()" />
      <view
        class="flex-1 center pr-100rpx font-500 text-32rpx c-#000 font-[PingFangSC,PingFang_SC]"
      ></view>
    </view>
    <scroll-view
      scroll-y
      scroll-with-animation
      :scroll-top="scrollTop"
      :throttle="false"
      @scroll="onScroll"
    >
      <view class="w-100vw bg-#FCF3F3">
        <view
          class="overflow-hidden flex items-center justify-center h-120rpx"
          :style="{ paddingTop: safeAreaInsets?.top + 'px' }"
        ></view>
        <view class="center">内容由AI生成,仅供参考</view>
        <view
          class="h520rpx"
          :style="{
            backgroundImage: `url(${getImagesUrl('68596505e4b00e5b2cf36190.png')})`,
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
          }"
        ></view>
        <view
          class="lh-39rpx text-27rpx fw-bold font-[SourceHanSerifCN] c-#000 text-center mb-32rpx"
        >
          五行属「{{ testResults?.fiveElement }}」，喜用「{{ testResults?.enjoyUse }}」
        </view>
        <view class="mx-32rpx sticky-tab-class" :style="{ top: safeAreaInsets?.top + 50 + 'px' }">
          <LuckyTabBar :tabs="tabList" :max="7" :activeIndex="active" :offset="64" />
        </view>
        <view class="m-32rpx b-rd-20rpx bg-#fff pt-15rpx category" :key="1">
          <view
            class="lh-60rpx text-30rpx fw-bold font-[SourceHanSerifCN] c-#C5171F w-290rpx h-60rpx pl-38rpx"
            style="
              background-image: url('../../static/images/mldzTitle.png');
              background-repeat: no-repeat;
              background-size: cover;
            "
          >
            今日运势
          </view>
          <view
            class="lh-36rpx text-25rpx fw-bold font-[SourceHanSerifCN] c-#000 pt36rpx pr-33rpx pb-44rpx pl-38rpx"
          >
            {{ testResults?.todayFortune }}
          </view>
        </view>
        <view class="m-32rpx mt-0 b-rd-20rpx bg-#fff pt-15rpx category" :key="0">
          <view
            class="lh-60rpx text-30rpx fw-bold font-[SourceHanSerifCN] c-#C5171F w-290rpx h-60rpx pl-38rpx"
            style="
              background-image: url('../../static/images/mldzTitle.png');
              background-repeat: no-repeat;
              background-size: cover;
            "
          >
            综合运势
          </view>
          <view
            class="lh-36rpx text-25rpx fw-bold font-[SourceHanSerifCN] c-#000 pt36rpx pr-33rpx pb-44rpx pl-38rpx"
          >
            {{ testResults?.overallFortune }}
          </view>
        </view>
        <view class="m-32rpx b-rd-20rpx bg-#fff pt-15rpx category" :key="2">
          <view
            class="lh-60rpx text-30rpx fw-bold font-[SourceHanSerifCN] c-#C5171F w-290rpx h-60rpx pl-38rpx"
            style="
              background-image: url('../../static/images/mldzTitle.png');
              background-repeat: no-repeat;
              background-size: cover;
            "
          >
            财运
          </view>
          <view
            class="lh-36rpx text-25rpx fw-bold font-[SourceHanSerifCN] c-#000 pt36rpx pr-33rpx pb-44rpx pl-38rpx"
          >
            {{ testResults?.moneyLuck }}
          </view>
        </view>
        <view class="m-32rpx b-rd-20rpx bg-#fff pt-15rpx category" :key="3">
          <view
            class="lh-60rpx text-30rpx fw-bold font-[SourceHanSerifCN] c-#C5171F w-290rpx h-60rpx pl-38rpx"
            style="
              background-image: url('../../static/images/mldzTitle.png');
              background-repeat: no-repeat;
              background-size: cover;
            "
          >
            桃花运
          </view>
          <view
            class="lh-36rpx text-25rpx fw-bold font-[SourceHanSerifCN] c-#000 pt36rpx pr-33rpx pb-44rpx pl-38rpx"
          >
            {{ testResults?.romanticLuck }}
          </view>
        </view>
        <view class="m-32rpx b-rd-20rpx bg-#fff pt-15rpx category" :key="4">
          <view
            class="lh-60rpx text-30rpx fw-bold font-[SourceHanSerifCN] c-#C5171F w-290rpx h-60rpx pl-38rpx"
            style="
              background-image: url('../../static/images/mldzTitle.png');
              background-repeat: no-repeat;
              background-size: cover;
            "
          >
            健康
          </view>
          <view
            class="lh-36rpx text-25rpx fw-bold font-[SourceHanSerifCN] c-#000 pt36rpx pr-33rpx pb-44rpx pl-38rpx"
          >
            {{ testResults?.healthLuck }}
          </view>
        </view>
        <view class="m-32rpx b-rd-20rpx bg-#fff pt-15rpx category" :key="5">
          <view
            class="lh-60rpx text-30rpx fw-bold font-[SourceHanSerifCN] c-#C5171F w-290rpx h-60rpx pl-38rpx"
            style="
              background-image: url('../../static/images/mldzTitle.png');
              background-repeat: no-repeat;
              background-size: cover;
            "
          >
            宝石推荐
          </view>
          <view
            class="lh-36rpx text-25rpx fw-bold font-[SourceHanSerifCN] c-#000 pt36rpx pr-33rpx pb-44rpx pl-38rpx"
          >
            <view v-for="(item, index) of testResults?.gemstoneRecommendations" :key="index">
              {{ item.name }} &nbsp; {{ item.reason }}
            </view>
          </view>
        </view>
        <view class="m-32rpx b-rd-20rpx bg-#fff pt-15rpx category" :key="6">
          <view
            class="lh-60rpx text-30rpx fw-bold font-[SourceHanSerifCN] c-#C5171F w-290rpx h-60rpx pl-38rpx"
            style="
              background-image: url('../../static/images/mldzTitle.png');
              background-repeat: no-repeat;
              background-size: cover;
            "
          >
            推荐佩戴
          </view>
          <view class="m20rpx">
            <wd-row>
              <wd-col :span="12" v-for="item in testResults?.list" :key="item.id">
                <view class="goodsItemClass" @click="goToProductDetails(item)">
                  <image :src="item.picture" class="goodsImagesClass" mode="aspectFill" />
                  <view class="goodsDescribeClass">{{ item.name }}</view>
                </view>
              </wd-col>
            </wd-row>
          </view>
          <view class="flex justify-center items-center pb-40rpx">
            <view class="flex-1 text-center">
              <wd-button type="error" :round="false" custom-class="custom" plain @click="goToHome">
                查看更多
              </wd-button>
            </view>
            <view class="flex-1 text-center">
              <wd-button type="error" :round="false" custom-class="custom" plain @click="goToDiy">
                我要定制
              </wd-button>
            </view>
          </view>
        </view>
        <view class="productDetailsButtonBodyPlaceholderClass" v-if="sharingMode"></view>
      </view>
    </scroll-view>
    <view class="productDetailsButtonBodyClass" v-if="sharingMode">
      <wd-button custom-class="custom" open-type="share">分享我的测试结果</wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import LuckyTabBar from '@/components/LuckyTabBar.vue'
import { getRect, isArray } from 'wot-design-uni/components/common/util'
import { onMounted, ref } from 'vue'
import { getImagesUrl } from '@/utils/getImagesUrl'
import { testResult, IAiChatItem, aiChat, shareDetail } from '@/service/feedbackOnOpinions/index'
import { useLoginGuard } from '@/composables/useTheme'
const { checkLogin } = useLoginGuard()
// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

const tabList = ['今日运势', '综合运势', '财运', '桃花运', '健康', '宝石推荐', '推荐佩戴']

onLoad((options) => {
  getShareDetail(options.id)
  testResultsId.value = options.id
  if (options.isShare === '0') {
    sharingMode.value = true
  } else {
    sharingMode.value = false
  }
})

// 返回上一级
function goBack() {
  const pages = getCurrentPages()
  if (pages.length > 1) {
    // 如果有上一级页面，返回上一页
    uni.navigateBack({
      delta: 1,
    })
  } else {
    // 如果已经是首页或栈底，重定向到首页
    uni.reLaunch({
      url: '/pages/index/index',
    })
  }
}

// 查看更多
function goToHome() {
  uni.reLaunch({
    url: '/pages/index/index',
  })
}
// 我要订制
function goToDiy() {
  uni.reLaunch({
    url: '/pages/diy/diy',
  })
}

// 订单详情数据
const testResults = ref<IAiChatItem>(testResult)
const testResultsId = ref<string>('')
const sharingMode = ref(false)

const active = ref<number>(0)
const scrollTop = ref<number>(0)
const itemScrollTop = ref<number[]>([])

// function handleChange({ value }) {
//   active.value = value
//   scrollTop.value = itemScrollTop.value[value] - 290
// }

// 跳转到商品详情
function goToProductDetails(item: { id: string }) {
  if (!checkLogin()) return
  uni.navigateTo({
    url: `/pages/productDetails/index?id=${item.id}`,
  })
}

function onScroll(e) {
  const { scrollTop } = e.detail
  const value = findClosestIndex(itemScrollTop.value, scrollTop + 420)
  active.value = value
}

function findClosestIndex(arr: number[], target: number): number {
  let closestIndex = 0
  let closestDiff = Infinity

  arr.forEach((val, index) => {
    const diff = Math.abs(val - target)
    if (diff < closestDiff) {
      closestDiff = diff
      closestIndex = index
    }
  })

  return closestIndex
}

function getShareDetail(id: string) {
  const { data, run } = useRequest<IAiChatItem>(() => shareDetail(id))
  run().then(() => {
    testResults.value = data.value
  })
}

// 分享
onShareAppMessage(() => {
  return {
    title: '命理定制',
    path: `pages/startTesting/index?id=${testResultsId.value}&isShare=1`,
  }
})

onMounted(() => {
  getRect('.category', true).then((rects) => {
    if (isArray(rects)) {
      itemScrollTop.value = rects.map((item) => item.top || 0)
    }
  })
})
</script>

<style lang="scss" scoped>
.goodsItemClass {
  margin: 12rpx;
  background: #ffffff;
  border-radius: 20rpx;
  .goodsImagesClass {
    height: 300rpx;
    background: #f8eded;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
  }
  .goodsDescribeClass {
    width: 311rpx;
    height: 64rpx;
    margin: 20rpx auto 0 auto;
    font-size: 25rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 32rpx;
    color: #000000;
    text-align: left;
  }
}

:deep() {
  .custom {
    width: 292rpx;
    font-size: 26rpx !important;
    font-weight: bold !important;
    color: #c5171f !important;
    border: 1rpx solid #c5171f !important;
    border-radius: 15rpx !important;
  }
}
.topTitle {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 100;
}

//商品底部按钮开始位置
.productDetailsButtonBodyPlaceholderClass {
  height: 188rpx;
}
.productDetailsButtonBodyClass {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  height: 188rpx;
  text-align: center;
  :deep() {
    //按钮样式
    .custom {
      width: 635rpx;
      height: 89rpx !important;
      font-size: 31rpx !important;
      font-weight: bold !important;
      color: #fff3e4 !important;
      border-radius: 15rpx !important;
    }
  }
  //商品底部按钮结束位置
}

.wraper {
  display: flex;
  height: calc(100vh - var(--window-top));
  height: calc(100vh - var(--window-top) - constant(safe-area-inset-bottom));
  height: calc(100vh - var(--window-top) - env(safe-area-inset-bottom));
}
.content {
  flex: 1;
  background: #fff;
}

.sticky-tab-class {
  position: sticky;
  z-index: 999;
  height: 220rpx;
  background-color: #fcf3f3; // 吸顶后避免遮挡内容
}
</style>
