<template>
  <view class="canvas-container">
    <!-- 网格背景辅助线 -->
    <view class="grid-background"></view>
    <canvas
      canvas-id="myCanvas"
      id="myCanvas"
      type="2d"
      :class="state.canvasClass"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    ></canvas>
  </view>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue'

interface Bead {
  id: string
  name: string
  image: string
  imagePath: string
  size: number
  materialName: string
  price: number
  uniqueId: number
  isEmptyHole?: boolean
  originalSize?: number
  originalBeadId?: string
  removedBead?: boolean
}

interface BeadInfo {
  index: number
  beadSize: number
  hasBead: boolean
  bead: Bead | null
  centerX?: number
  centerY?: number
  centerAngle?: number
  beadAngle?: number
}

interface Props {
  beads: Bead[]
  selectedIndex: number
  onBeadSelect?: (index: number) => void
  onBeadToggle?: (index: number) => void
}

const props = withDefaults(defineProps<Props>(), {
  beads: () => [],
  selectedIndex: -1,
  onBeadSelect: undefined,
  onBeadToggle: undefined,
})

const emit = defineEmits<{
  beadSelect: [index: number]
  beadToggle: [index: number]
}>()

let canvasEl = null
let ctx = null

const state = reactive({
  beadSize: 20, // 珠子大小
  totalBeads: 18, // 初始珠子孔位数量
  maxBeads: 40, // 最大珠子数量
  gapAngle: (Math.PI / 180) * 0.3, // 珠子间的间隔角度
  touchPosition: null, // 存储最近一次触摸的位置
  scale: 1, // 缩放比例
  rotation: 0, // 旋转角度（弧度）
  lastTouches: [],
  transformOrigin: { x: 150, y: 150 },
  canvasReady: false,
  animationTimer: null,
  translateY: 0,
  isInAnim: false,
  moveAnima: null,
  canvasClass: 'bracelet-canvas',
  beadInfoArray: [] as BeadInfo[], // 存储珠子信息
  actualRadius: null, // 保存实际使用的半径
  isProcessingDrawTasks: false, // 标记是否正在处理绘制任务
  beadDrawTaskTimer: null,
})

const originalState = {
  scale: 1,
  translateY: 0,
}

// 触摸事件处理函数
const handleTouchStart = (event) => {
  if (event.touches && event.touches.length > 0) {
    // 保存当前触摸点
    state.lastTouches = []
    for (let i = 0; i < event.touches.length; i++) {
      state.lastTouches.push({
        pageX: event.touches[i].pageX,
        pageY: event.touches[i].pageY,
      })
    }

    // 单点触摸 - 用于选择珠子
    if (event.touches.length === 1) {
      const touch = event.touches[0]

      try {
        const systemInfo = uni.getSystemInfoSync()
        const query = uni.createSelectorQuery().select('#myCanvas')
        query
          .boundingClientRect((data: any) => {
            if (data) {
              console.log('Canvas位置:', data)

              // 检查点击的Y坐标位置，如果超出canvas高度并进入底部区域，则不处理点击
              if (touch.pageY > data.top + data.height) {
                console.log('点击在底部区域，不处理珠子选择')
                return
              }

              const x = touch.pageX - data.left
              const y = touch.pageY - data.top

              console.log('触摸点坐标:', x, y, '原始坐标:', touch.pageX, touch.pageY)

              state.touchPosition = { x, y }

              checkBeadTouch(x, y)
            } else {
              console.error('无法获取Canvas位置信息')
              const x = touch.pageX
              const y = touch.pageY
              console.log('使用原始触摸坐标:', x, y)
              state.touchPosition = { x, y }
              checkBeadTouch(x, y)
            }
            smoothAnimate(state.scale, 0)
          })
          .exec()
      } catch (err) {
        console.error('获取坐标时出错:', err)
      }
    }
  }
}

// 触摸移动处理
const handleTouchMove = (event) => {
  // 检测是否是多点触摸（2个手指）
  if (event.touches && event.touches.length === 2 && state.lastTouches.length === 2) {
    const touch1 = event.touches[0]
    const touch2 = event.touches[1]
    const lastTouch1 = state.lastTouches[0]
    const lastTouch2 = state.lastTouches[1]
    const currentDistance = Math.sqrt(
      Math.pow(touch1.pageX - touch2.pageX, 2) + Math.pow(touch1.pageY - touch2.pageY, 2),
    )
    const lastDistance = Math.sqrt(
      Math.pow(lastTouch1.pageX - lastTouch2.pageX, 2) +
        Math.pow(lastTouch1.pageY - lastTouch2.pageY, 2),
    )
    const scaleChange = currentDistance / lastDistance
    const newScale = Math.max(0.5, Math.min(2.5, state.scale * scaleChange))

    const lastAngle = Math.atan2(
      lastTouch2.pageY - lastTouch1.pageY,
      lastTouch2.pageX - lastTouch1.pageX,
    )
    const currentAngle = Math.atan2(touch2.pageY - touch1.pageY, touch2.pageX - touch1.pageX)
    const rotationChange = currentAngle - lastAngle

    state.scale = newScale
    state.rotation = (state.rotation + rotationChange) % (Math.PI * 2)

    state.lastTouches = []
    for (let i = 0; i < event.touches.length; i++) {
      state.lastTouches.push({
        pageX: event.touches[i].pageX,
        pageY: event.touches[i].pageY,
      })
    }

    drawBracelet()
  }
}

// 检查触摸点是否在珠子上
const checkBeadTouch = (touchX, touchY) => {
  if (!ctx || !canvasEl) {
    console.error('Canvas未初始化，无法检查触摸')
    return
  }
  if (!state.beadInfoArray) {
    recalculateBeadPositions()
  }
  const centerX = state.transformOrigin.x
  const centerY = state.transformOrigin.y
  const relX = touchX - centerX
  const relY = touchY - centerY
  const cos = Math.cos(-state.rotation)
  const sin = Math.sin(-state.rotation)
  const rotatedX = relX * cos - relY * sin
  const rotatedY = relX * sin + relY * cos

  const scaledX = rotatedX / state.scale
  const scaledY = rotatedY / state.scale
  const transformedX = scaledX + centerX
  const transformedY = scaledY + centerY
  console.log('触摸变换:', '原始:', touchX, touchY, '变换后:', transformedX, transformedY)

  const clipRatio = 0.95
  const tolerance = 1.0

  let closestBeadIndex = -1
  let minDistance = Infinity

  // 使用预先计算好的珠子位置信息检查点击
  for (const info of state.beadInfoArray) {
    const x = info.centerX
    const y = info.centerY
    const distance = Math.sqrt(Math.pow(transformedX - x, 2) + Math.pow(transformedY - y, 2))
    const actualBeadSize = info.beadSize * clipRatio * tolerance
    if (distance < minDistance) {
      minDistance = distance
      closestBeadIndex = info.index
    }
    if (distance <= actualBeadSize) {
      console.log(
        '选中珠子:',
        info.index,
        '位置:',
        x,
        y,
        '距离:',
        distance,
        '孔大小:',
        actualBeadSize,
      )
      toggleBead(info.index)
      return // 找到匹配的珠子后立即返回
    }
  }

  if (closestBeadIndex !== -1) {
    const closestInfo = state.beadInfoArray.find((info) => info.index === closestBeadIndex)
    const maxAcceptableDistance = closestInfo.beadSize * clipRatio * 1.2

    console.log(
      '最近珠子:',
      closestBeadIndex,
      '距离:',
      minDistance,
      '最大可接受距离:',
      maxAcceptableDistance,
    )

    if (minDistance <= maxAcceptableDistance) {
      console.log('选择最近的珠子:', closestBeadIndex)
      toggleBead(closestBeadIndex)
    } else {
      console.log('点击位置超出所有珠子的可接受范围')
    }
  }
}

// 切换珠子状态
const toggleBead = (index) => {
  console.log('切换珠子状态，当前选中:', props.selectedIndex, '新选中:', index)

  if (props.selectedIndex === index) {
    // 如果两次点击同一个珠子，则取消选择
    emit('beadToggle', -1)
    console.log('取消选择')
  } else {
    // 选择新的珠子
    emit('beadToggle', index)
    console.log('新选中珠子:', index)
  }

  // 重置绘制任务状态，确保每次点击都会触发珠子绘制
  state.isProcessingDrawTasks = false
  drawBracelet()
}

const handleTouchEnd = () => {}

// 特殊布局
const applySpecialLayout = (beadCount) => {
  if (!canvasEl || !ctx) {
    console.error('Canvas未初始化，无法应用特殊布局')
    return
  }

  const centerX = state.transformOrigin.x
  const centerY = state.transformOrigin.y
  const size = Math.min(canvasEl.width, canvasEl.height)
  const referenceRadius = size * 0.35
  const baseBeadSize = referenceRadius * 0.18

  const beadInfoArray = []

  let beadSize1, beadSize2, spacing2, avgSize3, radius3, angle, beadSize

  switch (beadCount) {
    case 1: // 单珠
      // 单珠居中放置
      beadInfoArray.push({
        index: 0,
        beadSize: props.beads[0].size ? baseBeadSize * (props.beads[0].size / 12) : baseBeadSize,
        hasBead: true,
        bead: props.beads[0],
        centerX,
        centerY,
        centerAngle: 0,
        beadAngle: 0,
      })
      break

    case 2: // 双珠垂直排列 - 紧贴在一起
      beadSize1 = props.beads[0].size ? baseBeadSize * (props.beads[0].size / 12) : baseBeadSize
      beadSize2 = props.beads[1].size ? baseBeadSize * (props.beads[1].size / 12) : baseBeadSize
      spacing2 = (beadSize1 + beadSize2) * 0.5

      beadInfoArray.push({
        index: 0,
        beadSize: beadSize1,
        hasBead: true,
        bead: props.beads[0],
        centerX,
        centerY: centerY - spacing2 / 2,
        centerAngle: -Math.PI / 2,
        beadAngle: 0,
      })

      beadInfoArray.push({
        index: 1,
        beadSize: beadSize2,
        hasBead: true,
        bead: props.beads[1],
        centerX,
        centerY: centerY + spacing2 / 2,
        centerAngle: Math.PI / 2,
        beadAngle: 0,
      })
      break

    case 3: // 三珠三角形布局 - 紧贴在一起
      avgSize3 =
        (((props.beads[0].size ? props.beads[0].size : 12) +
          (props.beads[1].size ? props.beads[1].size : 12) +
          (props.beads[2].size ? props.beads[2].size : 12)) /
          36) *
        baseBeadSize
      radius3 = avgSize3 * 0.95
      for (let i = 0; i < 3; i++) {
        angle = (i * 2 * Math.PI) / 3 - Math.PI / 2
        beadSize = props.beads[i].size ? baseBeadSize * (props.beads[i].size / 12) : baseBeadSize

        beadInfoArray.push({
          index: i,
          beadSize,
          hasBead: true,
          bead: props.beads[i],
          centerX: centerX + radius3 * Math.cos(angle),
          centerY: centerY + radius3 * Math.sin(angle),
          centerAngle: angle,
          beadAngle: 0,
        })
      }
      break

    default:
      // 大于3颗珠子正常布局
      recalculateBeadPositions()
      return
  }
  state.beadInfoArray = beadInfoArray
}

// 重新计算所有珠子位置
const recalculateBeadPositions = () => {
  if (!canvasEl || !ctx) {
    console.error('Canvas未初始化，无法重新计算珠子位置')
    return
  }

  const centerX = state.transformOrigin.x
  const centerY = state.transformOrigin.y

  const size = Math.min(canvasEl.width, canvasEl.height)
  const referenceRadius = size * 0.35
  const gapAngle = (Math.PI / 180) * 0.3
  const baseBeadSize = referenceRadius * 0.18
  const beadInfoArray = []
  const actualBeadCount = props.beads.length
  if (actualBeadCount === 0) {
    return false
  }
  let maxBeadSize = 0
  for (let i = 0; i < actualBeadCount; i++) {
    const currentBead = props.beads[i]
    let beadSize = baseBeadSize

    if (currentBead) {
      if (currentBead.isEmptyHole && currentBead.originalSize) {
        const size = currentBead.originalSize
        if (size === 12) {
          beadSize = baseBeadSize * 1.0
        } else {
          const sizeRatio = size / 12
          beadSize = baseBeadSize * sizeRatio
        }
      } else if (currentBead.size) {
        if (currentBead.size === 12) {
          beadSize = baseBeadSize * 1.0
        } else {
          const sizeRatio = currentBead.size / 12
          beadSize = baseBeadSize * sizeRatio
        }
      }
    }

    maxBeadSize = Math.max(maxBeadSize, beadSize)

    beadInfoArray.push({
      index: i,
      beadSize,
      hasBead: !!(currentBead && !currentBead.isEmptyHole),
      bead: currentBead,
    })
  }

  function calculateTotalAngle(radius) {
    const safeRadius = Math.max(radius, maxBeadSize + 0.1)

    let totalAngle = 0
    for (const info of beadInfoArray) {
      if (info.beadSize >= safeRadius) {
        totalAngle += Math.PI
      } else {
        const ratio = info.beadSize / safeRadius
        const safeRatio = Math.min(Math.max(ratio, -1), 1)
        const beadAngle = 2 * Math.asin(safeRatio)
        totalAngle += beadAngle
      }
    }
    totalAngle += gapAngle * actualBeadCount
    return totalAngle
  }

  const minRequiredRadius = Math.max(
    maxBeadSize * 1.1,
    (beadInfoArray.reduce((sum, info) => sum + info.beadSize, 0) / (actualBeadCount * Math.PI)) * 2,
  )

  let minRadius = minRequiredRadius
  let maxRadius = referenceRadius * 2.0
  let idealRadius = referenceRadius
  const target = 2 * Math.PI
  const tolerance = 0.0001
  let iterations = 0

  if (idealRadius < minRadius) {
    idealRadius = minRadius
  }

  while (iterations < 20) {
    const totalAngle = calculateTotalAngle(idealRadius)
    const error = Math.abs(totalAngle - target)

    if (error < tolerance) {
      break
    }

    if (totalAngle > target) {
      minRadius = idealRadius
      idealRadius = (minRadius + maxRadius) / 2
    } else {
      maxRadius = idealRadius
      idealRadius = (minRadius + maxRadius) / 2
    }

    iterations++
  }

  idealRadius = Math.max(idealRadius, minRequiredRadius)
  const beadAngles = []
  let totalBeadAndGapAngle = 0

  for (let i = 0; i < beadInfoArray.length; i++) {
    const info = beadInfoArray[i]
    let beadAngle
    if (info.beadSize >= idealRadius) {
      beadAngle = Math.PI / 2
    } else {
      const ratio = info.beadSize / idealRadius
      const safeRatio = Math.min(Math.max(ratio, -1), 1)
      beadAngle = 2 * Math.asin(safeRatio)
    }

    beadAngles.push({
      index: i,
      beadAngle,
      gapAngle,
    })
    totalBeadAndGapAngle += beadAngle + gapAngle
  }
  const remainingAngle = Math.max(0, 2 * Math.PI - totalBeadAndGapAngle)
  const additionalGapAngle = actualBeadCount > 0 ? remainingAngle / actualBeadCount : 0
  const adjustedGapAngle = gapAngle + additionalGapAngle
  let currentAngle = -Math.PI / 2

  for (let i = 0; i < beadInfoArray.length; i++) {
    const info = beadInfoArray[i]
    const beadAngle = beadAngles[i].beadAngle
    const beadCenterAngle = currentAngle + beadAngle / 2
    const x = centerX + idealRadius * Math.cos(beadCenterAngle)
    const y = centerY + idealRadius * Math.sin(beadCenterAngle)
    info.centerX = x
    info.centerY = y
    info.centerAngle = beadCenterAngle
    info.beadAngle = beadAngle
    currentAngle += beadAngle + adjustedGapAngle
  }
  const finalAngle = currentAngle
  const expectedFinalAngle = -Math.PI / 2 + 2 * Math.PI
  const closingGap = Math.abs(finalAngle - expectedFinalAngle)
  const isClosedCircle = closingGap < 0.01
  const firstLastGapMatch = Math.abs(adjustedGapAngle - closingGap) < 0.01

  state.beadInfoArray = beadInfoArray
  state.actualRadius = idealRadius

  return isClosedCircle
}

const smoothAnimate = (targetScale, targetY) => {
  if (!ctx || !canvasEl) {
    console.error('Canvas上下文或元素不存在，无法执行动画')
    return
  }

  state.isInAnim = true

  if (state.beadDrawTaskTimer) {
    clearTimeout(state.beadDrawTaskTimer)
    state.beadDrawTaskTimer = null
  }

  if (state.animationTimer) {
    clearTimeout(state.animationTimer)
  }

  const startScale = state.scale
  const startY = state.translateY || 0
  const steps = 15
  const duration = 250
  const stepDuration = duration / steps

  let currentStep = 0

  const step = () => {
    if (!ctx || !canvasEl) {
      state.isInAnim = false
      console.error('Canvas上下文或元素在动画过程中丢失，停止动画')
      return
    }

    currentStep++

    const progress = currentStep / steps
    const easeProgress = 1 - Math.pow(1 - progress, 2)

    state.scale = startScale + (targetScale - startScale) * easeProgress
    state.translateY = startY + (targetY - startY) * easeProgress

    ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)
    state.isProcessingDrawTasks = false

    try {
      drawBracelet()
    } catch (err) {
      state.isInAnim = false
      console.error('动画中绘制出错:', err)
      return
    }

    if (currentStep < steps) {
      state.animationTimer = setTimeout(step, stepDuration)
    } else {
      state.isInAnim = false
    }
  }

  step()
}

// 修改为接收文本数组的气泡绘制函数
const drawPriceBubbles = (textArray) => {
  if (!ctx || !canvasEl || !textArray || textArray.length === 0) return

  const padding = 10
  const bubbleHeight = 32
  const cornerRadius = 15
  const marginLeft = 5
  const marginTop = 5
  const bubbleGap = 5 // 气泡之间的间距

  // 设置字体样式
  ctx.font = '14px sans-serif'

  // 保存所有气泡的宽度
  const bubbleWidths = textArray.map((text) => ctx.measureText(text).width + padding * 2)

  // 绘制所有气泡
  ctx.save()

  textArray.forEach((text, index) => {
    const y = marginTop + index * (bubbleHeight + bubbleGap)

    // 为每个气泡单独设置样式
    ctx.save()
    ctx.fillStyle = '#ffffff'
    ctx.shadowColor = 'rgba(0, 0, 0, 0.1)'
    ctx.shadowBlur = 10
    ctx.shadowOffsetX = 0
    ctx.shadowOffsetY = 2

    // 绘制气泡背景
    drawBubble(marginLeft, y, bubbleWidths[index], bubbleHeight, cornerRadius)
    ctx.restore()

    // 绘制文本
    ctx.fillStyle = '#333333'
    ctx.textBaseline = 'middle'
    ctx.fillText(text, marginLeft + padding, y + bubbleHeight / 2)
  })

  ctx.restore()
}

// 辅助函数：绘制单个气泡（保持不变）
const drawBubble = (x, y, width, height, radius) => {
  ctx.beginPath()
  ctx.moveTo(x + radius, y)
  ctx.lineTo(x + width - radius, y)
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
  ctx.lineTo(x + width, y + height - radius)
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
  ctx.lineTo(x + radius, y + height)
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
  ctx.lineTo(x, y + radius)
  ctx.quadraticCurveTo(x, y, x + radius, y)
  ctx.closePath()
  ctx.fill()
}

const drawBeadHoles = (centerX, centerY, radius) => {
  console.log(
    '开始绘制珠子孔，中心:',
    centerX,
    centerY,
    '初始半径:',
    radius,
    props.beads.findIndex((b) => b === undefined),
  )

  const beadInfoArray = state.beadInfoArray || []
  if (beadInfoArray.length === 0 && props.beads.length > 0) {
    console.error('警告：有珠子数据但没有珠子位置信息')
  }

  if (state.beadDrawTaskTimer) {
    clearTimeout(state.beadDrawTaskTimer)
    state.beadDrawTaskTimer = null
  }

  const drawTasks = []

  for (const info of beadInfoArray) {
    const currentBead = props.beads[info.index]

    if (
      info.index >= state.totalBeads &&
      (!currentBead || (currentBead.isEmptyHole && !currentBead.removedBead))
    ) {
      console.log('跳过绘制', info.index)
      continue
    }

    const x = info.centerX
    const y = info.centerY
    const bgColor = '#f5f5f5'
    const beadSize = info.beadSize
    const isSelected = info.index === props.selectedIndex

    if (isSelected) {
      const isSingleBead = beadInfoArray.length === 1
      let vectorX, vectorY

      if (isSingleBead) {
        vectorX = 0
        vectorY = 1
      } else {
        vectorX = centerX - x
        vectorY = centerY - y
      }

      const magnitude = Math.sqrt(vectorX * vectorX + vectorY * vectorY) || 1
      const normalizedX = vectorX / magnitude
      const normalizedY = vectorY / magnitude

      const shadowLayers = 6
      const maxShadowWidth = beadSize * 0.25
      for (let j = 0; j < shadowLayers; j++) {
        const layerAlpha = 0.35 - j * 0.05
        const layerWidth = beadSize + maxShadowWidth * (j / shadowLayers)
        const offsetRatio = j * 0.025
        const offsetX = x + normalizedX * offsetRatio * beadSize
        const offsetY = y + normalizedY * offsetRatio * beadSize

        ctx.beginPath()
        ctx.arc(offsetX, offsetY, layerWidth, 0, Math.PI * 2)
        ctx.fillStyle = `rgba(244, 67, 54, ${layerAlpha})`
        ctx.fill()
      }
    }

    if (currentBead && !currentBead.isEmptyHole) {
      ctx.beginPath()
      ctx.arc(x, y, beadSize, 0, Math.PI * 2)
      ctx.fillStyle = '#ffffff'
      ctx.fill()

      if (currentBead.imagePath && canvasEl && canvasEl.canvas) {
        drawTasks.push({
          type: 'bead',
          x,
          y,
          beadSize,
          isSelected,
          bead: currentBead,
          index: info.index,
        })
      } else {
        ctx.beginPath()
        ctx.arc(x, y, beadSize * 0.95, 0, Math.PI * 2)
        ctx.fillStyle = '#f0f0f0'
        ctx.fill()

        ctx.fillStyle = '#666'
        ctx.font = '10px sans-serif'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillText(currentBead.name || '珠子', x, y)

        if (isSelected) {
          ctx.beginPath()
          ctx.lineWidth = 1.5
          ctx.arc(x, y, beadSize, 0, Math.PI * 2)
          ctx.strokeStyle = '#f44336'
          ctx.stroke()
        }
      }
    } else {
      const clipRatio = 0.95
      ctx.beginPath()
      ctx.arc(x, y, beadSize * clipRatio, 0, Math.PI * 2)
      ctx.fillStyle = isSelected ? '#ffffff' : bgColor
      ctx.fill()

      ctx.beginPath()
      ctx.lineWidth = 1
      ctx.arc(x, y, beadSize * clipRatio, 0, Math.PI * 2)
      ctx.strokeStyle = isSelected ? '#f44336' : '#dddddd'
      ctx.stroke()
    }
  }

  if (beadInfoArray.length === 0) {
    ctx.font = '14px sans-serif'
    ctx.fillStyle = '#999999'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText('请从下方选择珠子添加', centerX, centerY - 20)
    ctx.fillText('或点击添加按钮开始', centerX, centerY + 20)
  }

  if (drawTasks.length > 0) {
    if (state.isProcessingDrawTasks) {
      return
    }

    state.isProcessingDrawTasks = true

    const transform = {
      centerX,
      centerY,
      translateY: state.translateY,
      rotation: state.rotation,
      scale: state.scale,
    }

    const allTasksCompleted = () => {
      state.isProcessingDrawTasks = false

      if (canvasEl.canvas && typeof canvasEl.canvas.requestAnimationFrame === 'function') {
        canvasEl.canvas.requestAnimationFrame(() => {
          console.log('所有珠子绘制任务完成')
        })
      }
    }

    let tasksCompleted = 0
    const totalTasks = drawTasks.length

    const taskCompleted = () => {
      tasksCompleted++
      if (tasksCompleted >= totalTasks) {
        allTasksCompleted()
      }
    }

    const taskPromises = drawTasks.map((task) => {
      return new Promise<void>((resolve) => {
        if (task.type === 'bead') {
          const uniqueUrl = `${task.bead.imagePath}&uid=${task.bead.uniqueId || Date.now()}&idx=${task.index}`

          if (beadImageCache[uniqueUrl]) {
            drawImageWithClipping(
              beadImageCache[uniqueUrl],
              task.x,
              task.y,
              task.beadSize,
              () => {
                if (task.isSelected) {
                  drawSelectedBorder(task.x, task.y, task.beadSize, transform)
                }
                resolve()
              },
              transform,
            )
          } else {
            loadBeadImage(uniqueUrl, (img) => {
              if (img) {
                drawImageWithClipping(
                  img,
                  task.x,
                  task.y,
                  task.beadSize,
                  () => {
                    if (task.isSelected) {
                      drawSelectedBorder(task.x, task.y, task.beadSize, transform)
                    }
                    resolve()
                  },
                  transform,
                )
              } else {
                resolve()
              }
            })
          }
        } else {
          resolve()
        }
      })
    })

    // 等待所有任务完成
    Promise.all(taskPromises)
      .then(() => {
        taskCompleted()
      })
      .catch((err) => {
        console.error('绘制任务出错:', err)
        state.isProcessingDrawTasks = false
      })
  }
}

// 绘制选中边框
function drawSelectedBorder(x, y, beadSize, transform) {
  ctx.save()
  ctx.beginPath()
  ctx.lineWidth = 1.5
  ctx.arc(x, y, beadSize, 0, Math.PI * 2)
  ctx.strokeStyle = '#f44336'
  ctx.stroke()
  ctx.restore()
}

function drawImageWithClipping(image, x, y, beadSize, callback, transform) {
  if (!image) {
    if (callback) callback()
    return
  }

  const clipRatio = 0.95
  try {
    ctx.save()

    // 裁剪和绘制
    ctx.beginPath()
    ctx.arc(x, y, beadSize * clipRatio, 0, Math.PI * 2)
    ctx.clip()
    const imageSize = beadSize * 2 * clipRatio
    ctx.drawImage(image, x - imageSize / 2, y - imageSize / 2, imageSize, imageSize)
    ctx.restore()

    if (callback) callback()
  } catch (e) {
    console.error('绘制图片出错:', e)
    if (ctx) ctx.restore()
    if (callback) callback()
  }
}

// 组件作用域增加图片缓存对象
const beadImageCache = {}

// 图片加载并缓存
function loadBeadImage(url, onload) {
  if (beadImageCache[url]) {
    onload(beadImageCache[url])
    return
  }
  const image = canvasEl.canvas.createImage()
  image.onload = () => {
    beadImageCache[url] = image
    onload(image)
  }
  image.onerror = (err) => {
    console.error('图片加载失败:', url, err)
    onload(null)
  }
  image.src = url
}

const drawBracelet = () => {
  if (!ctx) {
    console.error('Canvas上下文不存在')
    return
  }

  if (!canvasEl) {
    console.error('Canvas元素不存在')
    return
  }

  if (!state.beadInfoArray || state.beadInfoArray.length === 0) {
    if (props.beads.length <= 3 && props.beads.length > 0) {
      applySpecialLayout(props.beads.length)
    } else if (props.beads.length > 0) {
      recalculateBeadPositions()
    }
  }

  ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)

  // 设置透明背景
  ctx.fillStyle = 'rgba(0, 0, 0, 0)'
  ctx.fillRect(0, 0, canvasEl.width, canvasEl.height)

  // 先绘制气泡
  drawPriceBubbles(['2颗 周长1.3cm 推荐女性佩戴 ¥100', '红玛瑙 8cm ¥10.00'])

  // 再绘制手串
  const centerX = state.transformOrigin.x
  const centerY = state.transformOrigin.y
  const radius = state.actualRadius || Math.min(canvasEl.width, canvasEl.height) * 0.35
  ctx.save()

  ctx.translate(centerX, centerY + state.translateY)
  ctx.rotate(state.rotation)
  ctx.scale(state.scale, state.scale)
  ctx.translate(-centerX, -centerY)

  drawBeadHoles(centerX, centerY, radius)

  ctx.restore()

  if (canvasEl.canvas && typeof canvasEl.canvas.requestAnimationFrame === 'function') {
    canvasEl.canvas.requestAnimationFrame(() => {
      console.log('强制帧刷新完成')
    })
  }
}

// 初始化canvas
const initCanvas = () => {
  try {
    console.log('开始初始化Canvas')

    // 获取屏幕尺寸和安全区域信息
    const systemInfo = uni.getSystemInfoSync()
    const screenWidth = systemInfo.windowWidth

    // 获取安全区域尺寸
    const safeAreaInsets = {
      top: systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.top : 0,
      bottom: systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.bottom : 0,
    }

    console.log('安全区域:', safeAreaInsets)
    // 计算底部区域高度
    const bottomAreaHeight = 220 + safeAreaInsets.bottom
    const availableHeight = systemInfo.windowHeight - bottomAreaHeight - safeAreaInsets.top
    console.log(
      '屏幕尺寸:',
      screenWidth,
      'x',
      systemInfo.windowHeight,
      '可用高度:',
      availableHeight,
      '底部区域:',
      bottomAreaHeight,
    )
    canvasEl = {
      width: screenWidth,
      height: availableHeight,
    }
    state.transformOrigin = {
      x: screenWidth / 2,
      y: availableHeight / 2,
    }
    try {
      const query = uni.createSelectorQuery()
      query
        .select('#myCanvas')
        .fields({ node: true, size: true }, (res: any) => {
          if (res && res.node) {
            const canvas = res.node
            canvasEl.canvas = canvas
            ctx = canvas.getContext('2d')
            canvas.width = screenWidth * systemInfo.pixelRatio
            canvas.height = availableHeight * systemInfo.pixelRatio
            ctx.scale(systemInfo.pixelRatio, systemInfo.pixelRatio)

            // 设置透明背景
            ctx.clearRect(0, 0, canvas.width, canvas.height)
            ctx.fillStyle = 'rgba(0, 0, 0, 0)'
            ctx.fillRect(0, 0, canvas.width, canvas.height)

            if (!ctx) {
              console.error('无法获取Canvas 2D上下文')
              return
            }
            console.log('获取Canvas 2D上下文成功:', ctx)
            state.scale = 1
            state.rotation = 0
            state.canvasReady = true
            drawBracelet()
            canvasEl.node = canvas
          } else {
            console.error('无法获取Canvas节点')
          }
        })
        .exec()
    } catch (ctxError) {
      console.error('创建Canvas上下文出错:', ctxError)
    }
  } catch (error) {
    console.error('Canvas初始化出错:', error)
    canvasEl = null
    ctx = null
    state.canvasReady = false
  }
}

// 暴露给父组件的方法
const getCanvasElement = () => canvasEl
const getCanvasContext = () => ctx
const getCanvasState = () => state

// 重新绘制方法
const redraw = () => {
  if (state.canvasReady) {
    drawBracelet()
  }
}

// 动画方法
const animate = (targetScale: number, targetY: number) => {
  smoothAnimate(targetScale, targetY)
}

// 导出方法给父组件
defineExpose({
  getCanvasElement,
  getCanvasContext,
  getCanvasState,
  redraw,
  animate,
  initCanvas,
})

onMounted(() => {
  console.log('BraceletCanvas组件挂载完成，准备初始化Canvas')
  setTimeout(() => {
    console.log('开始尝试初始化Canvas')
    initCanvas()
  }, 300)
})

onBeforeUnmount(() => {
  if (state.animationTimer) {
    clearTimeout(state.animationTimer)
  }
  canvasEl = null
  ctx = null
})
</script>

<style lang="scss" scoped>
.canvas-container {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 60vh;
  max-height: 60vh;
  padding-bottom: 0;
  overflow: hidden;
  overflow-y: scroll;
  background: white;
  scroll-behavior: smooth;
}

.bracelet-canvas {
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  display: block;
  width: 100% !important;
  height: 100% !important;
  aspect-ratio: unset;
  touch-action: none;
  background: transparent;
}

/* 网格背景 */
.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(to right, #e0e0e0 1rpx, transparent 1rpx),
    linear-gradient(to bottom, #e0e0e0 1rpx, transparent 1rpx);
  background-size: 20rpx 20rpx;
  opacity: 0.6;
}
</style>
